import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { BaseService } from "../base/base.service"
import {
  ActivityPreferences,
  ActivityPreferencesCreateData,
  ActivityPreferencesUpdateData,
  DEFAULT_ACTIVITY_PREFERENCES,
} from "./activity-preferences.types"
import { UserSubscriptionService } from "../user-subscription/user-subscription.service"

/**
 * Activity preferences service for Firebase operations
 */
export class ActivityPreferencesService extends BaseService {
  private static readonly COLLECTION = "activityPreferences"

  /**
   * Get activity preferences by user ID
   * @param userId User ID
   * @returns Activity preferences or null if not found
   */
  static async getActivityPreferences(userId: string): Promise<ActivityPreferences | null> {
    try {
      // Check if user has Pro subscription
      const isSubscribed = await UserSubscriptionService.isUserSubscribed(userId)
      if (!isSubscribed) {
        return null
      }

      const preferences = await this.getById<ActivityPreferences>(this.COLLECTION, userId)

      // If no preferences exist, create default ones
      if (!preferences) {
        return await this.createDefaultActivityPreferences(userId)
      }

      return preferences
    } catch (error) {
      console.error("Error getting activity preferences:", error)
      throw error
    }
  }

  /**
   * Update activity preferences
   * @param userId User ID
   * @param preferencesData Preferences data to update
   * @returns Success status
   */
  static async updateActivityPreferences(
    userId: string,
    preferencesData: ActivityPreferencesUpdateData
  ): Promise<boolean> {
    try {
      // Check if user has Pro subscription
      const isSubscribed = await UserSubscriptionService.isUserSubscribed(userId)
      if (!isSubscribed) {
        throw new Error("Activity preferences are only available for Pro subscribers")
      }

      const docRef = doc(db, this.COLLECTION, userId)

      // Check if document exists
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        // Create new document with default preferences merged with updates
        const defaultPrefs = DEFAULT_ACTIVITY_PREFERENCES
        const newPreferences: ActivityPreferencesCreateData = {
          userId,
          eateries: { ...defaultPrefs.eateries, ...preferencesData.eateries },
          shopping: { ...defaultPrefs.shopping, ...preferencesData.shopping },
          entertainment: { ...defaultPrefs.entertainment, ...preferencesData.entertainment },
        }

        await setDoc(docRef, {
          ...newPreferences,
          id: userId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      } else {
        // Update existing document
        const updateData: any = {
          updatedAt: serverTimestamp(),
        }

        if (preferencesData.eateries) {
          Object.keys(preferencesData.eateries).forEach((key) => {
            updateData[`eateries.${key}`] =
              preferencesData.eateries![key as keyof typeof preferencesData.eateries]
          })
        }

        if (preferencesData.shopping) {
          Object.keys(preferencesData.shopping).forEach((key) => {
            updateData[`shopping.${key}`] =
              preferencesData.shopping![key as keyof typeof preferencesData.shopping]
          })
        }

        if (preferencesData.entertainment) {
          Object.keys(preferencesData.entertainment).forEach((key) => {
            updateData[`entertainment.${key}`] =
              preferencesData.entertainment![key as keyof typeof preferencesData.entertainment]
          })
        }

        await updateDoc(docRef, updateData)
      }

      return true
    } catch (error) {
      console.error("Error updating activity preferences:", error)
      throw error
    }
  }

  /**
   * Create default activity preferences for a user
   * @param userId User ID
   * @returns The created activity preferences
   */
  static async createDefaultActivityPreferences(userId: string): Promise<ActivityPreferences> {
    try {
      // Check if user has Pro subscription
      const isSubscribed = await UserSubscriptionService.isUserSubscribed(userId)
      if (!isSubscribed) {
        throw new Error("Activity preferences are only available for Pro subscribers")
      }

      const defaultPreferences: ActivityPreferencesCreateData = {
        userId,
        ...DEFAULT_ACTIVITY_PREFERENCES,
      }

      const docRef = doc(db, this.COLLECTION, userId)
      const newPreferences = {
        ...defaultPreferences,
        id: userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      await setDoc(docRef, newPreferences)

      // Return the preferences with proper timestamps
      return {
        ...defaultPreferences,
        id: userId,
        createdAt: null, // Will be set by Firestore
        updatedAt: null, // Will be set by Firestore
      }
    } catch (error) {
      console.error("Error creating default activity preferences:", error)
      throw error
    }
  }

  /**
   * Delete activity preferences (when user downgrades)
   * @param userId User ID
   * @returns Success status
   */
  static async deleteActivityPreferences(userId: string): Promise<boolean> {
    try {
      const docRef = doc(db, this.COLLECTION, userId)
      await setDoc(docRef, {
        id: userId,
        userId,
        eateries: DEFAULT_ACTIVITY_PREFERENCES.eateries,
        shopping: DEFAULT_ACTIVITY_PREFERENCES.shopping,
        entertainment: DEFAULT_ACTIVITY_PREFERENCES.entertainment,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })
      return true
    } catch (error) {
      console.error("Error deleting activity preferences:", error)
      throw error
    }
  }

  /**
   * Check if user can access activity preferences
   * @param userId User ID
   * @returns Whether user can access activity preferences
   */
  static async canAccessActivityPreferences(userId: string): Promise<boolean> {
    try {
      return await UserSubscriptionService.isUserSubscribed(userId)
    } catch (error) {
      console.error("Error checking activity preferences access:", error)
      return false
    }
  }
}
