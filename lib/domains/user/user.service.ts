import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { User, UserUpdateData } from "./user.types"

/**
 * User service for Firebase operations
 */
export class UserService {
  private static readonly COLLECTION = "users"

  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async getUser(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, userId))
      if (userDoc.exists()) {
        return { ...userDoc.data(), uid: userId } as User
      }
      return null
    } catch (error) {
      console.error("Error getting user:", error)
      throw error
    }
  }

  /**
   * Get multiple users by their IDs
   * @param userIds Array of user IDs to fetch
   * @returns Array of user data
   */
  static async getUsersFromIds(userIds: string[]): Promise<User[]> {
    try {
      // Ensure userIds is an array
      if (!Array.isArray(userIds)) {
        console.error("userIds is not an array:", userIds)
        return []
      }

      const users: User[] = []
      for (const userId of userIds) {
        const user = await this.getUser(userId)
        if (user) {
          users.push(user)
        }
      }
      return users
    } catch (error) {
      console.error("Error getting users from IDs:", error)
      throw error
    }
  }

  /**
   * Get a user by email
   * @param email User email
   * @returns The user data or null if not found
   */
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      const usersRef = collection(db, this.COLLECTION)
      const q = query(usersRef, where("email", "==", email))
      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0]
        return { ...userDoc.data(), uid: userDoc.id } as User
      }

      return null
    } catch (error) {
      console.error("Error getting user by email:", error)
      throw error
    }
  }

  /**
   * Update a user
   * @param userId User ID
   * @param userData User data to update
   * @returns Service response indicating success or failure
   */
  static async updateUser(userId: string, userData: UserUpdateData): Promise<ServiceResponse> {
    try {
      const userRef = doc(db, this.COLLECTION, userId)

      await updateDoc(userRef, {
        ...userData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating user:", error)
      return { success: false, error }
    }
  }

  /**
   * Ensure a user document exists
   * @param user Firebase user object
   * @returns The user data or null if creation failed
   */
  static async ensureUserDocument(user: any): Promise<User | null> {
    if (!user) return null

    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, user.uid))
      if (!userDoc.exists()) {
        // Create user document if it doesn't exist
        await setDoc(doc(db, this.COLLECTION, user.uid), {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || "",
          photoURL: user.photoURL || "",
          createdAt: serverTimestamp(),
          subscriptionPlan: "free",
          subscriptionStatus: null,
          newUser: true,
          firstLogin: serverTimestamp(),
        })
      }
      return userDoc.exists() ? ({ ...userDoc.data(), uid: user.uid } as User) : null
    } catch (error) {
      console.error("Error ensuring user document:", error)
      return null
    }
  }

  /**
   * Check if user is still in new user experience (within 7 days of first login)
   * @param userId User ID
   * @returns Boolean indicating if user is still new
   */
  static async isNewUser(userId: string): Promise<boolean> {
    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, userId))
      if (!userDoc.exists()) return false

      const userData = userDoc.data() as User

      // Only consider users new if newUser is explicitly true
      if (userData.newUser !== true) return false

      // If no firstLogin timestamp, assume not new
      if (!userData.firstLogin) return false

      // Check if within 7 days of first login
      const firstLoginDate = userData.firstLogin.toDate()
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      return firstLoginDate > sevenDaysAgo
    } catch (error) {
      console.error("Error checking new user status:", error)
      return false
    }
  }

  /**
   * Mark user as no longer new (end new user experience)
   * @param userId User ID
   * @returns Service response
   */
  static async endNewUserExperience(userId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, userId), {
        newUser: false,
        updatedAt: serverTimestamp(),
      })
      return { success: true }
    } catch (error) {
      console.error("Error ending new user experience:", error)
      return { success: false, error }
    }
  }

  /**
   * Update first login timestamp for tracking new user experience
   * @param userId User ID
   * @returns Service response
   */
  static async updateFirstLogin(userId: string): Promise<ServiceResponse> {
    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, userId))
      if (!userDoc.exists()) {
        return { success: false, error: new Error("User not found") }
      }

      const userData = userDoc.data() as User

      // Only update if firstLogin doesn't exist
      if (!userData.firstLogin) {
        await updateDoc(doc(db, this.COLLECTION, userId), {
          firstLogin: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating first login:", error)
      return { success: false, error }
    }
  }
}
