import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Subscription status type
 */
export type SubscriptionStatus =
  | "active"
  | "canceled"
  | "past_due"
  | "trialing"
  | "incomplete"
  | null

/**
 * Subscription plan type
 */
export type SubscriptionPlan = "free" | "monthly" | "yearly"

/**
 * User subscription entity
 */
export interface UserSubscription extends BaseEntity {
  userId: string
  stripeCustomerId: string
  subscriptionId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionPlan: SubscriptionPlan
  subscriptionCurrentPeriodEnd: Timestamp | number | null
}

/**
 * User subscription creation data
 */
export type UserSubscriptionCreateData = Omit<UserSubscription, "id" | "createdAt" | "updatedAt">

/**
 * User subscription update data
 */
export type UserSubscriptionUpdateData = Partial<
  Omit<UserSubscription, "id" | "userId" | "createdAt">
>

/**
 * User subscriptions map
 * Maps user IDs to subscription status (true if subscribed)
 */
export interface UserSubscriptionsMap {
  [userId: string]: boolean
}

/**
 * Subscription Error Types
 */
export enum SubscriptionErrorType {
  MAX_SQUADS_REACHED = "MAX_SQUADS_REACHED",
  MAX_TRIPS_PER_SQUAD_REACHED = "MAX_TRIPS_PER_SQUAD_REACHED",
  DAILY_AI_LIMIT_REACHED = "DAILY_AI_LIMIT_REACHED",
  WEEKLY_AI_LIMIT_REACHED = "WEEKLY_AI_LIMIT_REACHED",
  TRIP_AI_LIMIT_REACHED = "TRIP_AI_LIMIT_REACHED",
  TASK_AI_LIMIT_REACHED = "TASK_AI_LIMIT_REACHED",
  ITINERARY_AI_LIMIT_REACHED = "ITINERARY_AI_LIMIT_REACHED",
  GENERIC_ERROR = "GENERIC_ERROR",
}

/**
 * Subscription limits
 */
export const SUBSCRIPTION_LIMITS = {
  FREE: {
    MAX_SQUADS: 1,
    MAX_TRIPS_PER_SQUAD: 2,
    MAX_DAILY_AI_REQUESTS: 10,
    MAX_WEEKLY_AI_REQUESTS: 50,
    HAS_TRIP_CHAT: false,
  },
  PRO: {
    MAX_SQUADS: 5,
    MAX_TRIPS_PER_SQUAD: 3,
    MAX_DAILY_AI_REQUESTS: Infinity,
    MAX_WEEKLY_AI_REQUESTS: Infinity,
    HAS_TRIP_CHAT: true,
  },
}

/**
 * Subscription plan IDs
 */
export const PLANS = {
  MONTHLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY!,
  YEARLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY!,
}

/**
 * Subscription prices (for display purposes)
 */
export const SUBSCRIPTION_PRICES = {
  MONTHLY: 7.99,
  YEARLY: 79.99,
}
