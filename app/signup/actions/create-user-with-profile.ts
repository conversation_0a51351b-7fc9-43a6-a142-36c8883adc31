"use server"

import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth"
import { auth, db } from "@/lib/firebase"
import { doc, setDoc, serverTimestamp } from "firebase/firestore"
import { uploadProfilePictureAction } from "./upload-profile-picture"

export interface CreateUserWithProfileData {
  email: string
  password: string
  name: string
  bio?: string
  location?: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
}

export interface CreateUserWithProfileResult {
  success: boolean
  error?: string
  redirectUrl?: string
}

export async function createUserWithProfileAction(
  userData: CreateUserWithProfileData,
  profilePictureFormData?: FormData
): Promise<CreateUserWithProfileResult> {
  console.log("Create User With Profile Action:", {
    email: userData.email,
    hasProfilePicture: !!profilePictureFormData?.get("file"),
  })

  try {
    // Validate required fields
    if (!userData.email || !userData.password || !userData.name) {
      return {
        success: false,
        error: "Missing required fields: email, password, and name are required",
      }
    }

    let profilePictureURL: string | null = null

    // Upload profile picture first if provided
    if (profilePictureFormData?.get("file")) {
      console.log("Uploading profile picture...")
      const uploadResult = await uploadProfilePictureAction(profilePictureFormData)

      if (!uploadResult.success) {
        return {
          success: false,
          error: `Profile picture upload failed: ${uploadResult.error}`,
        }
      }

      profilePictureURL = uploadResult.url || null
      console.log("Profile picture uploaded successfully:", profilePictureURL)
    }

    // Create user account with Firebase Authentication
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    )

    const userId = userCredential.user.uid
    console.log("User created with ID:", userId)

    // Update the user profile with the display name and photo URL
    await updateProfile(userCredential.user, {
      displayName: userData.name,
      photoURL: profilePictureURL,
    })

    // 1. Save user document to Firestore
    const userRef = doc(db, "users", userId)
    await setDoc(userRef, {
      uid: userId,
      email: userData.email,
      displayName: userData.name,
      photoURL: profilePictureURL,
      bio: userData.bio || "",
      location: userData.location || null,
      locationPlaceId: userData.locationPlaceId || null,
      createdAt: serverTimestamp(),
      travelPreferences: userData.selectedTravelPreferences,
      budgetRange: userData.budget,
      availabilityPreferences: userData.selectedAvailability,
      preferredTravelSeasons: userData.selectedMonths,
      travelGroupPreferences: userData.selectedTravelGroups,
      newUser: true,
      firstLogin: serverTimestamp(),
    })

    // 2. Initialize user preferences document
    const preferencesRef = doc(db, "userPreferences", userId)
    await setDoc(preferencesRef, {
      userId,
      theme: "system",
      location: userData.location || null,
      locationPlaceId: userData.locationPlaceId || null,
      travelPreferences: userData.selectedTravelPreferences,
      budgetRange:
        userData.budget === "budget-friendly"
          ? [0, 500]
          : userData.budget === "mid-range"
            ? [500, 2000]
            : [2000, 10000],
      availabilityPreferences: userData.selectedAvailability,
      preferredTravelSeasons: userData.selectedMonths,
      travelGroupPreferences: userData.selectedTravelGroups,
      aiEnabled: true,
      proactiveSuggestions: true,
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      tripUpdatesNotifications: true,
      squadMessagesNotifications: true,
      invitationNotifications: true,
      aiSuggestionsNotifications: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    })

    // 3. Initialize user AI usage document
    const aiUsageRef = doc(db, "userAiUsage", userId)
    await setDoc(aiUsageRef, {
      userId,
      aiUsageToday: 0,
      aiUsageThisWeek: 0,
      aiUsageLastReset: serverTimestamp(),
      aiUsageWeekStart: serverTimestamp(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    })

    console.log("User documents created successfully")

    return {
      success: true,
      redirectUrl: "/login?message=account_created",
    }
  } catch (error: any) {
    console.error("Create user with profile error:", error)

    // Handle specific Firebase auth errors
    if (error.code === "auth/email-already-in-use") {
      return {
        success: false,
        error: "This email address is already registered. Please log in or use a different email.",
      }
    } else if (error.code === "auth/weak-password") {
      return {
        success: false,
        error: "Password is too weak. Please choose a stronger password.",
      }
    } else if (error.code === "auth/invalid-email") {
      return {
        success: false,
        error: "Invalid email address format.",
      }
    } else {
      return {
        success: false,
        error: error.message || "Something went wrong. Please try again.",
      }
    }
  }
}
