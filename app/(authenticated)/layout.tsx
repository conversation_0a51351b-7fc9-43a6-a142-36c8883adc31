"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"
import { useToast } from "@/components/ui/use-toast"
import { SubscriptionInitializer } from "@/components/subscription-initializer"
import { UserDataInitializer } from "@/components/user-data-initializer"
import { SettingsInitializer } from "@/components/settings-initializer"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"

export default function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuthStatus()
  const { toast } = useToast()
  const router = useRouter()
  const pathname = usePathname()

  // Define routes that should NOT have header/sidebar
  const fullscreenRoutes = [
    "/welcome",
    "/admin", // Admin routes have their own layout
  ]

  // Check if current route should be fullscreen
  const isFullscreenRoute = fullscreenRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    // Only redirect if we're not loading and there's no user
    if (!loading && !user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to access this page.",
        variant: "warning",
      })
      router.push("/login")
    }
  }, [user, loading, router])

  // Show loading state while checking authentication
  if (loading) {
    return <PageLoading message="Checking authentication..." />
  }

  // Don't render children until we confirm the user is authenticated
  if (!user) {
    return null
  }

  // User is authenticated, render the children with domain-specific initializers
  return (
    <>
      {/* Initialize domain-specific stores */}
      <UserDataInitializer />
      <SubscriptionInitializer />
      <SettingsInitializer />

      {isFullscreenRoute ? (
        // Fullscreen layout for special pages
        children
      ) : (
        // Standard layout with header and sidebar
        <div className="min-h-screen flex flex-col">
          <AppHeader />
          <div className="flex-1 flex">
            <AppSidebar />
            <main className="flex-1">{children}</main>
          </div>
        </div>
      )}
    </>
  )
}
